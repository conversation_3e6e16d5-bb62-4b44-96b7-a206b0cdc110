import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { modules } from './modulesData';
import ModuleStone from './ModuleStone';
import ModuleDetailModal from './ModuleDetailModal';
import { Module } from './types/GameData';

const Score: React.FC = () => {
  const navigate = useNavigate();
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleModuleClick = (module: Module): void => {
    setSelectedModule(module);
    setIsModalOpen(true);
  };

  const closeModal = (): void => {
    setIsModalOpen(false);
    setSelectedModule(null);
  };

  const handleBackToHome = (): void => {
    navigate('/home');
  };

  // Dramatic winding path with pronounced S-curves like the reference image
  const pathData = `
    M 120 480
    C 170 440, 200 400, 220 420
    C 250 450, 290 360, 320 380
    C 350 400, 100 320, 180 280
    C 260 240, 280 160, 320 200
    C 360 240, 410 260, 450 280
    C 490 300, 540 340, 580 380
    C 620 420, 680 460, 720 420
    C 760 380, 540 580, 620 520
    C 700 460, 780 440, 820 480
    C 860 520, 880 340, 920 380
    C 960 420, 980 240, 1020 280
    C 1060 320, 1120 360, 1180 320
  `;

  // Calculate precise positions along the dramatic path curves
  const getPathPositions = () => {
    // Positions calculated to sit perfectly on the path curves for optimal visual alignment
    return [
      { x: 120, y: 480 },  // Module 1 - Starting point (bottom left)
      { x: 220, y: 420 },  // Module 2 - First ascending curve
      { x: 320, y: 380 },  // Module 3 - Continuing upward trend
      { x: 180, y: 280 },  // Module 4 - Sharp switchback left (dramatic curve)
      { x: 320, y: 200 },  // Module 5 - Highest peak of the journey
      { x: 450, y: 280 },  // Module 6 - Descending curve right
      { x: 580, y: 380 },  // Module 7 - Mid-level curve section
      { x: 720, y: 420 },  // Module 8 - Right side elevation
      { x: 620, y: 520 },  // Module 9 - Dramatic dip down (lowest point)
      { x: 820, y: 480 },  // Module 10 - Rising back up
      { x: 920, y: 380 },  // Module 11 - Final ascent begins
      { x: 1020, y: 280 }, // Module 12 - End destination (top right)
    ];
  };

  const calculatedPositions = getPathPositions();



  return (
    <div className="w-full h-screen bg-gradient-to-br from-yellow-200 via-orange-200 to-amber-300 overflow-hidden">
      <div className="relative w-full h-full">
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 1200 600"
          className="absolute inset-0"
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Desert background pattern */}
          <defs>
            <pattern id="sandPattern" patternUnits="userSpaceOnUse" width="40" height="40">
              <circle cx="5" cy="5" r="1" fill="#F59E0B" opacity="0.1" />
              <circle cx="15" cy="15" r="0.8" fill="#EA580C" opacity="0.1" />
              <circle cx="25" cy="8" r="1.2" fill="#F97316" opacity="0.1" />
              <circle cx="35" cy="25" r="0.9" fill="#FB923C" opacity="0.1" />
            </pattern>
            
            <filter id="pathShadow">
              <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#92400E" floodOpacity="0.3"/>
            </filter>

            <linearGradient id="pathGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#D4A574" />
              <stop offset="50%" stopColor="#E8C4A0" />
              <stop offset="100%" stopColor="#D4A574" />
            </linearGradient>
          </defs>
          
          {/* Background with sand pattern */}
          <rect width="100%" height="100%" fill="url(#sandPattern)" />

          {/* Decorative landscape elements */}
          <ellipse cx="200" cy="550" rx="120" ry="25" fill="#D2691E" opacity="0.3" />
          <ellipse cx="500" cy="580" rx="150" ry="30" fill="#CD853F" opacity="0.25" />
          <ellipse cx="800" cy="560" rx="130" ry="28" fill="#DEB887" opacity="0.3" />
          <ellipse cx="1100" cy="570" rx="140" ry="32" fill="#D2691E" opacity="0.28" />

          {/* Small decorative rocks */}
          <circle cx="300" cy="500" r="8" fill="#8B4513" opacity="0.4" />
          <circle cx="600" cy="480" r="6" fill="#A0522D" opacity="0.4" />
          <circle cx="900" cy="520" r="7" fill="#8B4513" opacity="0.4" />
          <circle cx="400" cy="350" r="5" fill="#A0522D" opacity="0.4" />
          <circle cx="750" cy="300" r="6" fill="#8B4513" opacity="0.4" />
          
          {/* Winding path - main road with enhanced shadow */}
          <path
            d={pathData}
            fill="none"
            stroke="#B8860B"
            strokeWidth="60"
            strokeLinecap="round"
            strokeLinejoin="round"
            filter="url(#pathShadow)"
            opacity="0.7"
          />

          {/* Path main body */}
          <path
            d={pathData}
            fill="none"
            stroke="#D4A574"
            strokeWidth="45"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.9"
          />

          {/* Path border/highlight */}
          <path
            d={pathData}
            fill="none"
            stroke="#E8C4A0"
            strokeWidth="35"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.8"
          />

          {/* Inner path highlight */}
          <path
            d={pathData}
            fill="none"
            stroke="#F5DEB3"
            strokeWidth="25"
            strokeLinecap="round"
            strokeLinejoin="round"
            opacity="0.6"
          />

          {/* Dashed center line like in the reference image */}
          <path
            d={pathData}
            fill="none"
            stroke="#FFFFFF"
            strokeWidth="4"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeDasharray="15,10"
            opacity="0.9"
          />

          {/* Secondary dashed line for extra detail */}
          <path
            d={pathData}
            fill="none"
            stroke="rgba(255,255,255,0.5)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeDasharray="8,12"
            opacity="0.7"
          />
          
          
          {/* Module stones */}
          {modules.map((module: Module) => (
            <ModuleStone
              key={module.id}
              module={module}
              position={calculatedPositions[module.id - 1]}
              onClick={() => handleModuleClick(module)}
            />
          ))}
        </svg>

        {/* Back Button */}
        <div className="absolute top-6 left-6 z-20">
          <button
            onClick={handleBackToHome}
            className="bg-white bg-opacity-90 hover:bg-opacity-100 text-amber-900 font-bold py-2 px-4 rounded-lg shadow-lg transition-all duration-200 hover:scale-105 flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </button>
        </div>

        
        {/* Progress indicator */}
        <div className="absolute top-8 right-8 z-10 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
          <div className="text-sm font-medium text-gray-700 mb-2">Overall Progress</div>
          <div className="flex space-x-1">
            {modules.map((module) => (
              <div
                key={module.id}
                className={`w-3 h-3 rounded-full ${
                  module.status === 'completed' ? 'bg-green-500' :
                  module.status === 'unlocked' ? 'bg-yellow-500' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
          <div className="text-xs text-gray-600 mt-1">
            {modules.filter(m => m.status === 'completed').length} / {modules.length} completed
          </div>
        </div>
      </div>

      {/* Modal */}
      <ModuleDetailModal
        isOpen={isModalOpen}
        module={selectedModule}
        onClose={closeModal}
      />
    </div>
  );
};

export default Score;