import React, { useState } from 'react';
import { Lock, Star } from 'lucide-react';
import { ModuleStoneProps } from './types/GameData';

const ModuleStone: React.FC<ModuleStoneProps> = ({ module, position, onClick }) => {
  const { id, status, stars } = module;
  const { x, y } = position;
  const [isHovered, setIsHovered] = useState(false);

  const getModuleColor = (): string => {
    switch (status) {
      case 'completed':
        return '#10B981'; // Green
      case 'unlocked':
        return '#F59E0B'; // Amber
      case 'locked':
      default:
        return '#6B7280'; // Gray
    }
  };

  const isClickable: boolean = status !== 'locked';
  const scale = isHovered && isClickable ? 1.1 : 1;

  return (
    <g
      transform={`translate(${x}, ${y}) scale(${scale})`}
      className={`transition-all duration-300 ${
        isClickable ? 'cursor-pointer' : 'cursor-not-allowed'
      }`}
      onClick={isClickable ? onClick : undefined}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ transformOrigin: 'center' }}
    >
      {/* Shadow */}
      <ellipse
        cx="2"
        cy="38"
        rx="35"
        ry="8"
        fill={isHovered && isClickable ? "rgba(0,0,0,0.4)" : "rgba(0,0,0,0.2)"}
        className="transition-all duration-300"
      />
      
      {/* Main circle with gradient */}
      <defs>
        <radialGradient id={`gradient-${id}`} cx="30%" cy="30%">
          <stop offset="0%" stopColor={getModuleColor()} stopOpacity="1" />
          <stop offset="100%" stopColor={getModuleColor()} stopOpacity="0.7" />
        </radialGradient>
      </defs>
      
      <circle
        r="32"
        fill={`url(#gradient-${id})`}
        stroke={isHovered && isClickable ? "#2D3748" : "#4A5568"}
        strokeWidth={isHovered && isClickable ? "4" : "3"}
        className="drop-shadow-lg transition-all duration-300"
      />

      {/* Inner highlight */}
      <circle
        r="28"
        fill="none"
        stroke={isHovered && isClickable ? "rgba(255,255,255,0.5)" : "rgba(255,255,255,0.3)"}
        strokeWidth="2"
        className="transition-all duration-300"
      />
      
      {/* Content based on type and status */}
      {status === 'locked' ? (
        <Lock
          size={20}
          color="white"
          className="translate-x-[-10px] translate-y-[-10px]"
        />
      ) : (
        <text
          textAnchor="middle"
          dy="6"
          fontSize="18"
          fontWeight="bold"
          fill="white"
          className="select-none"
        >
          {id}
        </text>
      )}
      
      {/* Stars for completed modules */}
      {status === 'completed' && stars > 0 && (
        <g transform="translate(-24, -45)">
          {[...Array(3)].map((_, i) => (
            <Star
              key={i}
              size={12}
              x={i * 16}
              fill={i < stars ? '#FCD34D' : 'none'}
              stroke={i < stars ? '#F59E0B' : '#9CA3AF'}
              strokeWidth="1"
              className="transition-all duration-300"
            />
          ))}
        </g>
      )}
      
      {/* Pulse animation for next unlocked module */}
      {status === 'unlocked' && (
        <circle
          r="35"
          fill="none"
          stroke={getModuleColor()}
          strokeWidth="3"
          opacity="0.6"
          className="animate-pulse"
        />
      )}

      {/* Hover glow effect */}
      {isHovered && isClickable && (
        <circle
          r="38"
          fill="none"
          stroke={getModuleColor()}
          strokeWidth="2"
          opacity="0.4"
          className="transition-all duration-300"
        />
      )}
    </g>
  );
};

export default ModuleStone;